"""
Arabic Text Processing Utilities for TTS
Handles Arabic text normalization, diacritics, and character mapping
"""

import re
import unicodedata
from typing import Dict, List, Optional
import arabic_reshaper
from bidi.algorithm import get_display


class ArabicTextProcessor:
    """Arabic text processor for TTS preprocessing"""
    
    def __init__(self, remove_diacritics: bool = True, normalize_numbers: bool = True):
        self.remove_diacritics = remove_diacritics
        self.normalize_numbers = normalize_numbers
        
        # Arabic diacritics (Tashkeel)
        self.diacritics = [
            '\u064B',  # Fathatan
            '\u064C',  # Dammatan
            '\u064D',  # Ka<PERSON>ratan
            '\u064E',  # Fatha
            '\u064F',  # Damma
            '\u0650',  # Ka<PERSON>ra
            '\u0651',  # Shadda
            '\u0652',  # Sukun
            '\u0653',  # Maddah
            '\u0654',  # Hamza above
            '\u0655',  # Hamza below
            '\u0656',  # Subscript alef
            '\u0657',  # Inverted damma
            '\u0658',  # Mark noon ghunna
            '\u0659',  # Z<PERSON><PERSON>y
            '\u065A',  # Vowel sign small v above
            '\u065B',  # Vowel sign inverted small v above
            '\u065C',  # Vowel sign dot below
            '\u065D',  # Reversed damma
            '\u065E',  # Fatha with two dots
            '\u065F',  # Wavy hamza below
            '\u0670',  # Superscript alef
        ]
        
        # Arabic-Indic to Western-Arabic numerals mapping
        self.arabic_indic_digits = {
            '٠': '0', '١': '1', '٢': '2', '٣': '3', '٤': '4',
            '٥': '5', '٦': '6', '٧': '7', '٨': '8', '٩': '9'
        }
        
        # Number words in Arabic
        self.number_words = {
            '0': 'صفر', '1': 'واحد', '2': 'اثنان', '3': 'ثلاثة', '4': 'أربعة',
            '5': 'خمسة', '6': 'ستة', '7': 'سبعة', '8': 'ثمانية', '9': 'تسعة',
            '10': 'عشرة', '11': 'أحد عشر', '12': 'اثنا عشر', '13': 'ثلاثة عشر',
            '14': 'أربعة عشر', '15': 'خمسة عشر', '16': 'ستة عشر', '17': 'سبعة عشر',
            '18': 'ثمانية عشر', '19': 'تسعة عشر', '20': 'عشرون', '30': 'ثلاثون',
            '40': 'أربعون', '50': 'خمسون', '60': 'ستون', '70': 'سبعون',
            '80': 'ثمانون', '90': 'تسعون', '100': 'مائة', '1000': 'ألف'
        }
        
        # Common punctuation normalization
        self.punctuation_map = {
            '؟': '?',  # Arabic question mark
            '؛': ';',  # Arabic semicolon
            '،': ',',  # Arabic comma
        }
    
    def remove_diacritics_from_text(self, text: str) -> str:
        """Remove Arabic diacritics from text"""
        for diacritic in self.diacritics:
            text = text.replace(diacritic, '')
        return text
    
    def normalize_arabic_indic_digits(self, text: str) -> str:
        """Convert Arabic-Indic digits to Western-Arabic digits"""
        for arabic_digit, western_digit in self.arabic_indic_digits.items():
            text = text.replace(arabic_digit, western_digit)
        return text
    
    def normalize_punctuation(self, text: str) -> str:
        """Normalize Arabic punctuation to standard forms"""
        for arabic_punct, standard_punct in self.punctuation_map.items():
            text = text.replace(arabic_punct, standard_punct)
        return text
    
    def convert_numbers_to_words(self, text: str) -> str:
        """Convert simple numbers to Arabic words"""
        if not self.normalize_numbers:
            return text
        
        # Simple number conversion (1-99)
        def number_to_arabic_word(match):
            number = int(match.group())
            if number in [int(k) for k in self.number_words.keys()]:
                return self.number_words[str(number)]
            elif 21 <= number <= 99:
                tens = (number // 10) * 10
                ones = number % 10
                if ones == 0:
                    return self.number_words[str(tens)]
                else:
                    return f"{self.number_words[str(ones)]} و{self.number_words[str(tens)]}"
            return str(number)
        
        # Replace standalone numbers
        text = re.sub(r'\b\d{1,2}\b', number_to_arabic_word, text)
        return text
    
    def clean_text(self, text: str) -> str:
        """Clean and normalize Arabic text"""
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text.strip())
        
        # Remove non-Arabic, non-punctuation characters (except spaces and numbers)
        text = re.sub(r'[^\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF\s\d\.,!?;:]', '', text)
        
        # Normalize Unicode
        text = unicodedata.normalize('NFKC', text)
        
        return text
    
    def process_text(self, text: str) -> str:
        """Main text processing pipeline"""
        # Clean text
        text = self.clean_text(text)
        
        # Normalize punctuation
        text = self.normalize_punctuation(text)
        
        # Normalize Arabic-Indic digits
        text = self.normalize_arabic_indic_digits(text)
        
        # Convert numbers to words
        text = self.convert_numbers_to_words(text)
        
        # Remove diacritics if specified
        if self.remove_diacritics:
            text = self.remove_diacritics_from_text(text)
        
        # Final cleanup
        text = re.sub(r'\s+', ' ', text.strip())
        
        return text
    
    def reshape_arabic_text(self, text: str) -> str:
        """Reshape Arabic text for proper display (if needed)"""
        try:
            reshaped_text = arabic_reshaper.reshape(text)
            return get_display(reshaped_text)
        except Exception:
            return text
    
    def validate_arabic_text(self, text: str) -> bool:
        """Check if text contains Arabic characters"""
        arabic_pattern = re.compile(r'[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]')
        return bool(arabic_pattern.search(text))


def test_arabic_processor():
    """Test function for Arabic text processor"""
    processor = ArabicTextProcessor()
    
    test_texts = [
        "مَرْحَباً بِكُمْ فِي قَنَاتِي",  # With diacritics
        "اليوم سنتعلم الذكاء الاصطناعي",  # Simple text
        "العدد ١٢٣ والرقم 456",  # Mixed numbers
        "هذا سؤال؟ وهذه جملة؛ وهذه أخرى،",  # Punctuation
    ]
    
    print("Arabic Text Processor Test:")
    print("=" * 50)
    
    for text in test_texts:
        processed = processor.process_text(text)
        print(f"Original:  {text}")
        print(f"Processed: {processed}")
        print(f"Valid Arabic: {processor.validate_arabic_text(text)}")
        print("-" * 30)


if __name__ == "__main__":
    test_arabic_processor()
