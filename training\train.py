#!/usr/bin/env python3
"""
Training Script for Arabic TTS using Coqui TTS
Supports both Tacotron2 and HiFi-GAN training
"""

import os
import sys
import argparse
import json
from pathlib import Path
import torch
from TTS.tts.configs.tacotron2_config import Tacotron2Config
from TTS.vocoder.configs.hifigan_config import HifiganConfig
from TTS.tts.models.tacotron2 import Tacotron2
from TTS.vocoder.models.gan import GAN
from TTS.trainer import Trainer, TrainerArgs
from TTS.utils.audio import AudioProcessor
from TTS.tts.datasets import load_tts_samples
from TTS.vocoder.datasets import load_wav_data


class ArabicTTSTrainer:
    """Arabic TTS training manager"""
    
    def __init__(self, config_path: str, dataset_path: str, output_path: str):
        self.config_path = Path(config_path)
        self.dataset_path = Path(dataset_path)
        self.output_path = Path(output_path)
        
        # Load configuration
        with open(self.config_path, 'r', encoding='utf-8') as f:
            self.config_dict = json.load(f)
        
        self.model_type = self.config_dict.get('model', 'tacotron2')
        
        # Create output directory
        self.output_path.mkdir(parents=True, exist_ok=True)
        
        print(f"Initializing {self.model_type} trainer...")
        print(f"Dataset: {self.dataset_path}")
        print(f"Output: {self.output_path}")
    
    def setup_tacotron2_training(self):
        """Setup Tacotron2 training configuration"""
        print("Setting up Tacotron2 training...")
        
        # Create Tacotron2 config
        config = Tacotron2Config()
        
        # Update config with our settings
        config.update(self.config_dict)
        
        # Set paths
        config.output_path = str(self.output_path)
        config.datasets[0]['path'] = str(self.dataset_path)
        
        # Audio processor
        ap = AudioProcessor.init_from_config(config)
        
        # Load dataset
        train_samples, eval_samples = load_tts_samples(
            config.datasets,
            eval_split=True,
            eval_split_max_size=config.eval_split_max_size,
            eval_split_size=config.eval_split_size,
        )
        
        print(f"Training samples: {len(train_samples)}")
        print(f"Evaluation samples: {len(eval_samples)}")
        
        # Initialize model
        model = Tacotron2(config, ap, tokenizer=None, speaker_manager=None)
        
        return config, model, train_samples, eval_samples, ap
    
    def setup_hifigan_training(self):
        """Setup HiFi-GAN training configuration"""
        print("Setting up HiFi-GAN training...")
        
        # Create HiFi-GAN config
        config = HifiganConfig()
        
        # Update config with our settings
        config.update(self.config_dict)
        
        # Set paths
        config.output_path = str(self.output_path)
        config.datasets[0]['path'] = str(self.dataset_path)
        
        # Audio processor
        ap = AudioProcessor.init_from_config(config)
        
        # Load dataset
        train_samples, eval_samples = load_wav_data(config.datasets, eval_split=True)
        
        print(f"Training samples: {len(train_samples)}")
        print(f"Evaluation samples: {len(eval_samples)}")
        
        # Initialize model
        model = GAN(config, ap)
        
        return config, model, train_samples, eval_samples, ap
    
    def train_model(self, resume_checkpoint: str = None):
        """Train the model"""
        print(f"Starting {self.model_type} training...")
        
        # Setup based on model type
        if self.model_type == 'tacotron2':
            config, model, train_samples, eval_samples, ap = self.setup_tacotron2_training()
        elif self.model_type == 'hifigan':
            config, model, train_samples, eval_samples, ap = self.setup_hifigan_training()
        else:
            raise ValueError(f"Unsupported model type: {self.model_type}")
        
        # Trainer arguments
        trainer_args = TrainerArgs()
        trainer_args.restore_path = resume_checkpoint
        
        # Initialize trainer
        trainer = Trainer(
            trainer_args,
            config,
            str(self.output_path),
            model=model,
            train_samples=train_samples,
            eval_samples=eval_samples,
        )
        
        # Start training
        trainer.fit()
        
        print("Training completed!")
        
        return trainer


def main():
    parser = argparse.ArgumentParser(description="Train Arabic TTS model")
    parser.add_argument("--config", type=str, required=True,
                       help="Path to configuration file")
    parser.add_argument("--dataset", type=str, required=True,
                       help="Path to dataset directory")
    parser.add_argument("--output", type=str, required=True,
                       help="Output directory for model")
    parser.add_argument("--resume", type=str, default=None,
                       help="Resume from checkpoint")
    parser.add_argument("--gpu", type=int, default=0,
                       help="GPU device ID")
    
    args = parser.parse_args()
    
    # Set GPU
    if torch.cuda.is_available():
        torch.cuda.set_device(args.gpu)
        print(f"Using GPU: {args.gpu}")
    else:
        print("CUDA not available, using CPU")
    
    # Initialize trainer
    trainer = ArabicTTSTrainer(args.config, args.dataset, args.output)
    
    # Start training
    trainer.train_model(args.resume)


if __name__ == "__main__":
    main()
