{"model": "<PERSON><PERSON><PERSON>", "run_name": "arabic_hifigan", "run_description": "Arabic HiFi-GAN Vocoder", "audio": {"sample_rate": 22050, "hop_length": 256, "win_length": 1024, "n_fft": 1024, "n_mels": 80, "mel_fmin": 0, "mel_fmax": 8000, "power": 1.0, "preemphasis": 0.0}, "model_params": {"upsample_rates": [8, 8, 2, 2], "upsample_kernel_sizes": [16, 16, 4, 4], "upsample_initial_channel": 512, "resblock_kernel_sizes": [3, 7, 11], "resblock_dilation_sizes": [[1, 3, 5], [1, 3, 5], [1, 3, 5]], "resblock_type": "1", "gen_istft_n_fft": 16, "gen_istft_hop_size": 4}, "discriminator_params": {"periods_multi": [2, 3, 5, 7, 11], "period_discriminator_params": {"in_channels": 1, "out_channels": 1, "kernel_sizes": [5, 3], "channels": 32, "downsample_scales": [3, 3, 3, 3, 1], "max_downsample_channels": 1024, "bias": true, "nonlinear_activation": "LeakyReLU", "nonlinear_activation_params": {"negative_slope": 0.1}, "use_weight_norm": true, "use_spectral_norm": false}, "scale_discriminator_params": {"in_channels": 1, "out_channels": 1, "kernel_sizes": [15, 41, 5, 3], "channels": 128, "max_downsample_channels": 1024, "max_groups": 16, "bias": true, "downsample_scales": [2, 2, 4, 4, 1], "nonlinear_activation": "LeakyReLU", "nonlinear_activation_params": {"negative_slope": 0.1}}}, "training": {"batch_size": 16, "eval_batch_size": 8, "num_loader_workers": 4, "num_eval_loader_workers": 4, "run_eval": true, "test_delay_epochs": 5, "epochs": 1000, "seq_len": 8192, "pad_short": 2000, "use_noise_augment": false, "eval_split_size": 10, "print_step": 25, "print_eval": false, "mixed_precision": false, "lr_gen": 0.0002, "lr_disc": 0.0002, "optimizer": "AdamW", "optimizer_params": {"betas": [0.8, 0.99], "eps": 1e-09, "weight_decay": 0.01}, "lr_scheduler_gen": "ExponentialLR", "lr_scheduler_gen_params": {"gamma": 0.999, "last_epoch": -1}, "lr_scheduler_disc": "ExponentialLR", "lr_scheduler_disc_params": {"gamma": 0.999, "last_epoch": -1}}, "loss_params": {"stft_loss_params": {"n_ffts": [1024, 2048, 512], "hop_lengths": [120, 240, 50], "win_lengths": [600, 1200, 240]}, "subband_stft_loss_params": {"n_ffts": [384, 683, 171], "hop_lengths": [30, 60, 10], "win_lengths": [150, 300, 60]}, "stft_loss_weight": 0.5, "subband_stft_loss_weight": 0.5, "feature_loss_weight": 2.0, "discriminator_loss_weight": 1.0, "generator_loss_weight": 1.0}, "datasets": [{"name": "arabic_dataset", "path": "./dataset/", "meta_file_train": "metadata.csv", "meta_file_val": "metadata.csv"}], "output_path": "./models/hifigan/", "save_step": 1000, "checkpoint": true, "keep_all_best": false, "keep_after": 10000, "num_checkpoints": 5}