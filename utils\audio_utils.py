"""
Audio Processing Utilities for Arabic TTS
Handles audio preprocessing, validation, and normalization
"""

import os
import numpy as np
import librosa
import soundfile as sf
from typing import Tuple, Optional, List
import warnings
from pathlib import Path


class AudioProcessor:
    """Audio processor for TTS preprocessing and validation"""
    
    def __init__(self, 
                 sample_rate: int = 22050,
                 hop_length: int = 256,
                 win_length: int = 1024,
                 n_fft: int = 1024,
                 n_mels: int = 80,
                 mel_fmin: float = 0.0,
                 mel_fmax: float = 8000.0,
                 preemphasis: float = 0.97,
                 ref_level_db: float = 20.0,
                 min_level_db: float = -100.0):
        
        self.sample_rate = sample_rate
        self.hop_length = hop_length
        self.win_length = win_length
        self.n_fft = n_fft
        self.n_mels = n_mels
        self.mel_fmin = mel_fmin
        self.mel_fmax = mel_fmax
        self.preemphasis = preemphasis
        self.ref_level_db = ref_level_db
        self.min_level_db = min_level_db
        
        # Mel filter bank
        self.mel_basis = librosa.filters.mel(
            sr=sample_rate,
            n_fft=n_fft,
            n_mels=n_mels,
            fmin=mel_fmin,
            fmax=mel_fmax
        )
    
    def load_audio(self, path: str) -> Tuple[np.ndarray, int]:
        """Load audio file and return waveform and sample rate"""
        try:
            audio, sr = librosa.load(path, sr=None, mono=True)
            return audio, sr
        except Exception as e:
            raise ValueError(f"Error loading audio file {path}: {str(e)}")
    
    def save_audio(self, audio: np.ndarray, path: str, sample_rate: int = None) -> None:
        """Save audio to file"""
        if sample_rate is None:
            sample_rate = self.sample_rate
        
        # Ensure directory exists
        os.makedirs(os.path.dirname(path), exist_ok=True)
        
        # Normalize audio to prevent clipping
        audio = self.normalize_audio(audio)
        
        sf.write(path, audio, sample_rate, subtype='PCM_16')
    
    def resample_audio(self, audio: np.ndarray, orig_sr: int, target_sr: int = None) -> np.ndarray:
        """Resample audio to target sample rate"""
        if target_sr is None:
            target_sr = self.sample_rate
        
        if orig_sr != target_sr:
            audio = librosa.resample(audio, orig_sr=orig_sr, target_sr=target_sr)
        
        return audio
    
    def normalize_audio(self, audio: np.ndarray, target_db: float = -20.0) -> np.ndarray:
        """Normalize audio to target dB level"""
        # Calculate RMS
        rms = np.sqrt(np.mean(audio**2))
        
        if rms > 0:
            # Convert target dB to linear scale
            target_rms = 10**(target_db / 20.0)
            # Scale audio
            audio = audio * (target_rms / rms)
        
        # Clip to prevent overflow
        audio = np.clip(audio, -1.0, 1.0)
        
        return audio
    
    def trim_silence(self, audio: np.ndarray, top_db: int = 30, frame_length: int = 2048, hop_length: int = 512) -> np.ndarray:
        """Trim silence from beginning and end of audio"""
        audio_trimmed, _ = librosa.effects.trim(
            audio, 
            top_db=top_db,
            frame_length=frame_length,
            hop_length=hop_length
        )
        return audio_trimmed
    
    def apply_preemphasis(self, audio: np.ndarray) -> np.ndarray:
        """Apply preemphasis filter"""
        if self.preemphasis > 0:
            return np.append(audio[0], audio[1:] - self.preemphasis * audio[:-1])
        return audio
    
    def remove_preemphasis(self, audio: np.ndarray) -> np.ndarray:
        """Remove preemphasis filter"""
        if self.preemphasis > 0:
            return np.append(audio[0], audio[1:] + self.preemphasis * audio[:-1])
        return audio
    
    def compute_mel_spectrogram(self, audio: np.ndarray) -> np.ndarray:
        """Compute mel spectrogram from audio"""
        # Apply preemphasis
        audio = self.apply_preemphasis(audio)
        
        # Compute STFT
        stft = librosa.stft(
            audio,
            n_fft=self.n_fft,
            hop_length=self.hop_length,
            win_length=self.win_length,
            window='hann',
            center=True,
            pad_mode='reflect'
        )
        
        # Magnitude spectrogram
        magnitude = np.abs(stft)
        
        # Mel spectrogram
        mel_spec = np.dot(self.mel_basis, magnitude)
        
        # Convert to dB
        mel_spec_db = self.amplitude_to_db(mel_spec)
        
        return mel_spec_db
    
    def amplitude_to_db(self, magnitude: np.ndarray) -> np.ndarray:
        """Convert amplitude to dB"""
        return 20 * np.log10(np.maximum(1e-5, magnitude)) - self.ref_level_db
    
    def db_to_amplitude(self, db: np.ndarray) -> np.ndarray:
        """Convert dB to amplitude"""
        return 10**((db + self.ref_level_db) / 20)
    
    def normalize_mel(self, mel_spec: np.ndarray) -> np.ndarray:
        """Normalize mel spectrogram"""
        return np.clip((mel_spec - self.min_level_db) / (-self.min_level_db), 0, 1)
    
    def denormalize_mel(self, mel_spec: np.ndarray) -> np.ndarray:
        """Denormalize mel spectrogram"""
        return (mel_spec * (-self.min_level_db)) + self.min_level_db
    
    def validate_audio_file(self, file_path: str) -> dict:
        """Validate audio file and return properties"""
        try:
            audio, sr = self.load_audio(file_path)
            
            duration = len(audio) / sr
            rms = np.sqrt(np.mean(audio**2))
            max_amplitude = np.max(np.abs(audio))
            
            # Check for clipping
            clipping_ratio = np.sum(np.abs(audio) > 0.99) / len(audio)
            
            # Check for silence
            silence_ratio = np.sum(np.abs(audio) < 0.01) / len(audio)
            
            return {
                'valid': True,
                'duration': duration,
                'sample_rate': sr,
                'channels': 1,  # We force mono
                'rms': rms,
                'max_amplitude': max_amplitude,
                'clipping_ratio': clipping_ratio,
                'silence_ratio': silence_ratio,
                'file_size': os.path.getsize(file_path),
                'recommended_resample': sr != self.sample_rate
            }
            
        except Exception as e:
            return {
                'valid': False,
                'error': str(e)
            }
    
    def preprocess_audio_file(self, input_path: str, output_path: str = None, 
                            trim_silence: bool = True, normalize: bool = True) -> str:
        """Preprocess audio file for TTS training"""
        # Load audio
        audio, sr = self.load_audio(input_path)
        
        # Resample if needed
        if sr != self.sample_rate:
            audio = self.resample_audio(audio, sr, self.sample_rate)
        
        # Trim silence
        if trim_silence:
            audio = self.trim_silence(audio)
        
        # Normalize
        if normalize:
            audio = self.normalize_audio(audio)
        
        # Save processed audio
        if output_path is None:
            output_path = input_path
        
        self.save_audio(audio, output_path, self.sample_rate)
        
        return output_path
    
    def batch_validate_audio_files(self, file_paths: List[str]) -> dict:
        """Validate multiple audio files"""
        results = {}
        valid_count = 0
        total_duration = 0
        
        for file_path in file_paths:
            result = self.validate_audio_file(file_path)
            results[file_path] = result
            
            if result['valid']:
                valid_count += 1
                total_duration += result['duration']
        
        return {
            'individual_results': results,
            'summary': {
                'total_files': len(file_paths),
                'valid_files': valid_count,
                'invalid_files': len(file_paths) - valid_count,
                'total_duration': total_duration,
                'average_duration': total_duration / valid_count if valid_count > 0 else 0
            }
        }


def test_audio_processor():
    """Test function for audio processor"""
    processor = AudioProcessor()
    
    print("Audio Processor Test:")
    print("=" * 50)
    print(f"Sample Rate: {processor.sample_rate}")
    print(f"Hop Length: {processor.hop_length}")
    print(f"N FFT: {processor.n_fft}")
    print(f"N Mels: {processor.n_mels}")
    print("=" * 50)
    
    # Create a test audio signal
    duration = 2.0  # seconds
    t = np.linspace(0, duration, int(processor.sample_rate * duration))
    test_audio = 0.5 * np.sin(2 * np.pi * 440 * t)  # 440 Hz sine wave
    
    # Test mel spectrogram computation
    mel_spec = processor.compute_mel_spectrogram(test_audio)
    print(f"Test audio shape: {test_audio.shape}")
    print(f"Mel spectrogram shape: {mel_spec.shape}")
    
    # Test normalization
    normalized_audio = processor.normalize_audio(test_audio)
    print(f"Original RMS: {np.sqrt(np.mean(test_audio**2)):.4f}")
    print(f"Normalized RMS: {np.sqrt(np.mean(normalized_audio**2)):.4f}")


if __name__ == "__main__":
    test_audio_processor()
