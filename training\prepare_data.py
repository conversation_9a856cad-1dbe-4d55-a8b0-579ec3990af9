#!/usr/bin/env python3
"""
Data Preparation Script for Arabic TTS
Validates, preprocesses, and prepares dataset for training
"""

import os
import sys
import pandas as pd
import argparse
from pathlib import Path
from typing import List, Dict, Tuple
import shutil
from tqdm import tqdm

# Add utils to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..'))

from utils.arabic_text_processor import ArabicTextProcessor
from utils.audio_utils import AudioProcessor


class DatasetPreparator:
    """Handles dataset preparation for Arabic TTS training"""
    
    def __init__(self, dataset_path: str, output_path: str = None):
        self.dataset_path = Path(dataset_path)
        self.output_path = Path(output_path) if output_path else self.dataset_path
        
        self.text_processor = ArabicTextProcessor()
        self.audio_processor = AudioProcessor()
        
        self.metadata_file = self.dataset_path / "metadata.csv"
        self.wavs_dir = self.dataset_path / "wavs"
        
        # Statistics
        self.stats = {
            'total_files': 0,
            'valid_files': 0,
            'invalid_files': 0,
            'total_duration': 0.0,
            'processed_files': 0
        }
    
    def validate_dataset_structure(self) -> bool:
        """Validate basic dataset structure"""
        print("Validating dataset structure...")
        
        if not self.dataset_path.exists():
            print(f"❌ Dataset path does not exist: {self.dataset_path}")
            return False
        
        if not self.metadata_file.exists():
            print(f"❌ Metadata file not found: {self.metadata_file}")
            return False
        
        if not self.wavs_dir.exists():
            print(f"❌ WAV directory not found: {self.wavs_dir}")
            return False
        
        print("✅ Dataset structure is valid")
        return True
    
    def load_and_validate_metadata(self) -> pd.DataFrame:
        """Load and validate metadata CSV"""
        print("Loading metadata...")
        
        try:
            # Try different separators
            for sep in ['|', ',', '\t']:
                try:
                    df = pd.read_csv(self.metadata_file, sep=sep, header=None)
                    if len(df.columns) >= 2:
                        break
                except:
                    continue
            else:
                raise ValueError("Could not parse metadata file with any separator")
            
            # Assign column names
            if len(df.columns) == 2:
                df.columns = ['filename', 'text']
            else:
                print(f"⚠️  Metadata has {len(df.columns)} columns, using first 2")
                df = df.iloc[:, :2]
                df.columns = ['filename', 'text']
            
            print(f"✅ Loaded {len(df)} entries from metadata")
            
            # Validate entries
            valid_entries = []
            for idx, row in df.iterrows():
                filename = str(row['filename']).strip()
                text = str(row['text']).strip()
                
                # Check if file exists
                wav_path = self.wavs_dir / filename
                if not wav_path.exists():
                    print(f"⚠️  Audio file not found: {filename}")
                    continue
                
                # Check if text is valid Arabic
                if not self.text_processor.validate_arabic_text(text):
                    print(f"⚠️  No Arabic text found in: {text[:50]}...")
                    continue
                
                valid_entries.append({
                    'filename': filename,
                    'text': text,
                    'wav_path': wav_path
                })
            
            valid_df = pd.DataFrame(valid_entries)
            print(f"✅ {len(valid_df)} valid entries after validation")
            
            return valid_df
            
        except Exception as e:
            print(f"❌ Error loading metadata: {str(e)}")
            return pd.DataFrame()
    
    def validate_audio_files(self, df: pd.DataFrame) -> pd.DataFrame:
        """Validate audio files in the dataset"""
        print("Validating audio files...")
        
        valid_entries = []
        
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Validating audio"):
            wav_path = row['wav_path']
            
            # Validate audio file
            audio_info = self.audio_processor.validate_audio_file(str(wav_path))
            
            if not audio_info['valid']:
                print(f"❌ Invalid audio file: {row['filename']} - {audio_info['error']}")
                continue
            
            # Check duration (should be between 1-10 seconds for TTS)
            duration = audio_info['duration']
            if duration < 1.0 or duration > 10.0:
                print(f"⚠️  Audio duration out of range: {row['filename']} ({duration:.2f}s)")
                # Still include but warn
            
            # Check for excessive clipping
            if audio_info['clipping_ratio'] > 0.01:  # More than 1% clipping
                print(f"⚠️  High clipping detected: {row['filename']} ({audio_info['clipping_ratio']:.2%})")
            
            # Add audio info to row
            row_dict = row.to_dict()
            row_dict.update(audio_info)
            valid_entries.append(row_dict)
            
            self.stats['total_duration'] += duration
        
        valid_df = pd.DataFrame(valid_entries)
        self.stats['total_files'] = len(df)
        self.stats['valid_files'] = len(valid_df)
        self.stats['invalid_files'] = len(df) - len(valid_df)
        
        print(f"✅ Audio validation complete: {len(valid_df)}/{len(df)} files valid")
        
        return valid_df
    
    def preprocess_text(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess text in the dataset"""
        print("Preprocessing text...")
        
        processed_texts = []
        
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing text"):
            original_text = row['text']
            processed_text = self.text_processor.process_text(original_text)
            
            processed_texts.append({
                'filename': row['filename'],
                'original_text': original_text,
                'processed_text': processed_text,
                'wav_path': row['wav_path']
            })
        
        processed_df = pd.DataFrame(processed_texts)
        print("✅ Text preprocessing complete")
        
        return processed_df
    
    def preprocess_audio_files(self, df: pd.DataFrame) -> pd.DataFrame:
        """Preprocess audio files"""
        print("Preprocessing audio files...")
        
        # Create output directory
        output_wavs_dir = self.output_path / "wavs"
        output_wavs_dir.mkdir(parents=True, exist_ok=True)
        
        processed_entries = []
        
        for idx, row in tqdm(df.iterrows(), total=len(df), desc="Processing audio"):
            input_path = row['wav_path']
            output_filename = row['filename']
            output_path = output_wavs_dir / output_filename
            
            try:
                # Preprocess audio
                self.audio_processor.preprocess_audio_file(
                    str(input_path),
                    str(output_path),
                    trim_silence=True,
                    normalize=True
                )
                
                processed_entries.append({
                    'filename': output_filename,
                    'processed_text': row['processed_text'],
                    'original_text': row['original_text'],
                    'output_path': output_path
                })
                
                self.stats['processed_files'] += 1
                
            except Exception as e:
                print(f"❌ Error processing {row['filename']}: {str(e)}")
                continue
        
        processed_df = pd.DataFrame(processed_entries)
        print(f"✅ Audio preprocessing complete: {len(processed_df)} files processed")
        
        return processed_df
    
    def create_final_metadata(self, df: pd.DataFrame) -> None:
        """Create final metadata file for training"""
        print("Creating final metadata...")
        
        # Create metadata in TTS format
        metadata_lines = []
        for idx, row in df.iterrows():
            # Format: filename|text
            line = f"{row['filename']}|{row['processed_text']}"
            metadata_lines.append(line)
        
        # Save metadata
        output_metadata = self.output_path / "metadata.csv"
        with open(output_metadata, 'w', encoding='utf-8') as f:
            f.write('\n'.join(metadata_lines))
        
        print(f"✅ Final metadata saved: {output_metadata}")
        
        # Also save detailed metadata with original text
        detailed_metadata = self.output_path / "metadata_detailed.csv"
        df[['filename', 'original_text', 'processed_text']].to_csv(
            detailed_metadata, index=False, encoding='utf-8'
        )
        
        print(f"✅ Detailed metadata saved: {detailed_metadata}")
    
    def print_statistics(self) -> None:
        """Print dataset statistics"""
        print("\n" + "="*50)
        print("DATASET STATISTICS")
        print("="*50)
        print(f"Total files: {self.stats['total_files']}")
        print(f"Valid files: {self.stats['valid_files']}")
        print(f"Invalid files: {self.stats['invalid_files']}")
        print(f"Processed files: {self.stats['processed_files']}")
        print(f"Total duration: {self.stats['total_duration']:.2f} seconds ({self.stats['total_duration']/60:.2f} minutes)")
        
        if self.stats['valid_files'] > 0:
            avg_duration = self.stats['total_duration'] / self.stats['valid_files']
            print(f"Average duration: {avg_duration:.2f} seconds")
        
        print("="*50)
    
    def prepare_dataset(self) -> bool:
        """Main dataset preparation pipeline"""
        print("Starting dataset preparation...")
        
        # Validate structure
        if not self.validate_dataset_structure():
            return False
        
        # Load and validate metadata
        df = self.load_and_validate_metadata()
        if df.empty:
            print("❌ No valid metadata found")
            return False
        
        # Validate audio files
        df = self.validate_audio_files(df)
        if df.empty:
            print("❌ No valid audio files found")
            return False
        
        # Preprocess text
        df = self.preprocess_text(df)
        
        # Preprocess audio files
        df = self.preprocess_audio_files(df)
        if df.empty:
            print("❌ No audio files could be processed")
            return False
        
        # Create final metadata
        self.create_final_metadata(df)
        
        # Print statistics
        self.print_statistics()
        
        print("✅ Dataset preparation complete!")
        return True


def main():
    parser = argparse.ArgumentParser(description="Prepare Arabic TTS dataset")
    parser.add_argument("--dataset_path", type=str, required=True,
                       help="Path to dataset directory")
    parser.add_argument("--output_path", type=str, default=None,
                       help="Output path (default: same as dataset_path)")
    
    args = parser.parse_args()
    
    preparator = DatasetPreparator(args.dataset_path, args.output_path)
    success = preparator.prepare_dataset()
    
    if not success:
        sys.exit(1)


if __name__ == "__main__":
    main()
