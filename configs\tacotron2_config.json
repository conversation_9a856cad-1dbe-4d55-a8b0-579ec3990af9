{"model": "tacotron2", "run_name": "arabic_tacotron2", "run_description": "Arabic TTS with Tacotron2", "audio": {"sample_rate": 22050, "hop_length": 256, "win_length": 1024, "n_fft": 1024, "n_mels": 80, "mel_fmin": 0, "mel_fmax": 8000, "preemphasis": 0.97, "ref_level_db": 20, "min_level_db": -100, "power": 1.5, "griffin_lim_iters": 60}, "model_params": {"n_symbols": 150, "symbols_embedding_dim": 512, "encoder_embedding_dim": 512, "encoder_n_convolutions": 3, "encoder_kernel_size": 5, "decoder_rnn_dim": 1024, "decoder_max_step": 2000, "decoder_dropout": 0.1, "decoder_early_stopping": true, "attention_rnn_dim": 1024, "attention_dim": 128, "attention_location_n_filters": 32, "attention_location_kernel_size": 31, "prenet_dim": 256, "postnet_embedding_dim": 512, "postnet_kernel_size": 5, "postnet_n_convolutions": 5, "postnet_dropout": 0.5, "use_memory_mask": true}, "training": {"batch_size": 32, "eval_batch_size": 16, "num_loader_workers": 4, "num_eval_loader_workers": 4, "run_eval": true, "test_delay_epochs": 10, "epochs": 1000, "lr": 0.001, "lr_decay": true, "warmup_steps": 4000, "windowing": false, "use_phonemes": false, "phoneme_cache_path": "./phoneme_cache", "print_step": 25, "print_eval": false, "mixed_precision": false, "seq_len_norm": false, "loss_masking": true, "ga_alpha": 5.0, "r": 1, "memory_size": -1, "prenet_dropout": true, "prenet_dropout_at_inference": false}, "optimizer": {"name": "RAdam", "lr": 0.001, "betas": [0.9, 0.998], "weight_decay": 1e-06}, "lr_scheduler": {"name": "NoamLR", "warmup_steps": 4000}, "datasets": [{"name": "arabic_dataset", "path": "./dataset/", "meta_file_train": "metadata.csv", "meta_file_val": "metadata.csv"}], "test_sentences": ["مرحبا بكم في قناتي", "اليوم سنتعلم الذكاء الاصطناعي", "هذا مثال على النص العربي", "شكرا لكم على المتابعة"], "output_path": "./models/tacotron2/", "save_step": 1000, "checkpoint": true, "keep_all_best": false, "keep_after": 10000, "num_checkpoints": 5}